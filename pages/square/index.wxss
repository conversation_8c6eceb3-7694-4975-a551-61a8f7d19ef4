/* pages/square/index.wxss */
page {
  --theme-color: #3B82F6;
  --bg-color: #f5f7fa;
  --card-color: #ffffff;
  --text-color: #1a1a1a;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-color: #e5e7eb;
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --radius-small: 8rpx;
  --radius-medium: 12rpx;
  --radius-large: 16rpx;
  --tabbar-height: 100rpx;
}

/* 深色模式变量 */
page[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --card-color: #2d2d2d;
  --text-color: #ffffff;
  --text-secondary: #cccccc;
  --text-light: #999999;
  --border-color: #404040;
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.square-container {
  width: 100%;
  min-height: 100vh;
  background-color: var(--bg-color);
  display: flex;
  flex-direction: column;
  padding-top: 192rpx; /* 搜索头部(112rpx) + 标签栏(80rpx) */
}

/* 搜索头部 */
.search-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: var(--card-color);
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  height: 112rpx; /* 明确设置高度 */
  box-sizing: border-box;
}

.search-box {
  flex: 1;
  height: 72rpx;
  background-color: var(--bg-color);
  border-radius: var(--radius-large);
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  gap: 16rpx;
}

.search-icon {
  font-size: 32rpx;
  color: var(--text-light);
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-light);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-color);
}

.notification-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.notification-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
}

/* 标签栏 */
.tags-section {
  position: fixed;
  top: 112rpx; /* 搜索头部的高度 */
  left: 0;
  right: 0;
  z-index: 99;
  background-color: var(--card-color);
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  height: 80rpx; /* 明确设置标签栏高度 */
  box-sizing: border-box;
}

.tags-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.tags-container {
  display: inline-flex;
  padding: 16rpx 32rpx;
  gap: 8rpx;
  min-width: 100%;
  align-items: center;
  height: 100%;
  box-sizing: border-box;
}

.tag-item {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  border-radius: var(--radius-large);
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0; /* 防止标签被压缩 */
  min-width: fit-content;
  height: 48rpx; /* 固定标签高度 */
  box-sizing: border-box;
}

.tag-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.tag-item.active {
  background-color: var(--theme-color);
}

.tag-item.active .tag-text {
  color: #ffffff;
  font-weight: 600;
}

.tag-indicator {
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 24rpx;
  height: 4rpx;
  background-color: var(--theme-color);
  border-radius: 2rpx;
}

/* 内容区域 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载状态 */
.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(59, 130, 246, 0.1);
  border-top-color: var(--theme-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 4rpx;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-light);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 内容滚动区域 */
.content-scroll {
  flex: 1;
  padding-bottom: var(--tabbar-height);
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
}

/* 内容列表 */
.content-list {
  padding: 24rpx 32rpx;
}

/* 内容卡片 */
.content-card {
  background-color: var(--card-color);
  border-radius: var(--radius-large);
  margin-bottom: 24rpx;
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  will-change: transform;
  contain: layout style paint;
}

.content-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx 16rpx;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.author-avatar-container {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: var(--bg-color);
}

.author-avatar-text {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 头像颜色主题 */
.avatar-color-0 { background-color: #FF6B6B; }
.avatar-color-1 { background-color: #4ECDC4; }
.avatar-color-2 { background-color: #45B7D1; }
.avatar-color-3 { background-color: #96CEB4; }
.avatar-color-4 { background-color: #FFEAA7; }
.avatar-color-5 { background-color: #DDA0DD; }
.avatar-color-6 { background-color: #98D8C8; }

.author-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.author-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
}

.post-time {
  font-size: 24rpx;
  color: var(--text-light);
}

.card-menu {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-color);
}

.menu-icon {
  font-size: 32rpx;
  color: var(--text-light);
  transform: rotate(90deg);
}

/* 卡片内容 */
.card-content {
  padding: 0 24rpx 16rpx;
}

.content-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 图片展示 */
.content-images {
  margin-bottom: 16rpx;
  border-radius: var(--radius-medium);
  overflow: hidden;
}

.content-image {
  width: 100%;
  height: 400rpx;
  background-color: var(--bg-color);
  margin-bottom: 8rpx;
}

.content-image:last-child {
  margin-bottom: 0;
}

.content-image.single {
  height: 480rpx;
}

.content-image.multiple {
  height: 320rpx;
}

/* 内容标签 */
.content-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.content-tag {
  font-size: 24rpx;
  color: var(--theme-color);
  background-color: rgba(59, 130, 246, 0.1);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-small);
  font-weight: 500;
}

/* 卡片底部 */
.card-footer {
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid var(--border-color);
}

.action-group {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: var(--radius-medium);
  transition: all 0.3s ease;
  background-color: transparent;
}

.action-btn:active {
  background-color: var(--bg-color);
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.like-btn.liked .action-icon {
  animation: heartBeat 0.6s ease-in-out;
}

.like-btn.liked .action-text {
  color: #ff4757;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* 加载更多状态 */
.load-more-state {
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: var(--text-light);
  text-align: center;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.empty-action {
  padding: 24rpx 48rpx;
  background-color: var(--theme-color);
  border-radius: var(--radius-large);
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: scale(0.95);
  background-color: #2563eb;
}

.empty-action .action-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 悬浮创建按钮 */
.fab-button {
  position: fixed;
  right: 32rpx;
  bottom: calc(var(--tabbar-height) + 32rpx);
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, var(--theme-color), #60a5fa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
  z-index: 1000;
  transition: all 0.3s ease;
}

.fab-button:active,
.fab-button.fab-hover {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  right: 32rpx;
  bottom: calc(var(--tabbar-height) + 160rpx);
  width: 88rpx;
  height: 88rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  opacity: 0;
  transform: scale(0.8) translateY(20rpx);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.back-to-top.show {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.back-to-top:active,
.back-to-top.back-to-top-hover {
  transform: scale(0.9);
  background-color: rgba(0, 0, 0, 0.8);
}

.back-to-top-icon {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .content-list {
    padding: 20rpx 24rpx;
  }

  .search-header {
    padding: 16rpx 24rpx;
  }

  .tags-container {
    padding: 0 24rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-card {
  animation: fadeInUp 0.4s ease-out;
}

/* 深色模式下的特殊样式 */
page[data-theme="dark"] .search-box {
  background-color: #333333;
}

page[data-theme="dark"] .action-btn:active {
  background-color: #333333;
}

page[data-theme="dark"] .content-tag {
  background-color: rgba(59, 130, 246, 0.2);
}

page[data-theme="dark"] .search-header {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

page[data-theme="dark"] .tags-section {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}