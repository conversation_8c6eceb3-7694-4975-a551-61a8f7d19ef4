<!--pages/square/index.wxml-->
<view class="square-container">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-box" bindtap="onSearchTap">
      <view class="search-icon">🔍</view>
      <text class="search-placeholder">搜索感兴趣的内容...</text>
    </view>
    <view class="header-actions">
      <view class="action-btn" bindtap="onNotificationTap">
        <view class="notification-icon">🔔</view>
        <view class="notification-badge" wx:if="{{hasNewNotification}}"></view>
      </view>
    </view>
  </view>

  <!-- 简化的标签栏 -->
  <view class="tags-section">
    <scroll-view
      class="tags-scroll"
      scroll-x="true"
      show-scrollbar="false"
      scroll-left="{{tagScrollLeft}}"
      scroll-with-animation="true">
      <view class="tags-container">
        <view
          class="tag-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{allCategories}}"
          wx:key="id"
          data-category="{{item.id}}"
          bindtap="onTagTap">
          <text class="tag-text">{{item.name}}</text>
          <view class="tag-indicator" wx:if="{{currentCategory === item.id}}"></view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容区域 -->
  <view class="content-section">
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 内容列表 -->
    <scroll-view
      class="content-scroll"
      scroll-y="true"
      refresher-enabled="true"
      refresher-triggered="{{isRefreshing}}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore"
      wx:if="{{!isLoading}}">

      <view class="content-list">
        <view
          class="content-card"
          wx:for="{{posts}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="onPostTap">

          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="author-info">
              <image class="author-avatar" src="{{item.author.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
              <view class="author-details">
                <text class="author-name">{{item.author.name}}</text>
                <text class="post-time">{{item.timeAgo}}</text>
              </view>
            </view>
            <view class="card-menu" data-id="{{item.id}}" catchtap="onCardMenu">
              <text class="menu-icon">⋯</text>
            </view>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <text class="content-title">{{item.title}}</text>
            <text class="content-text" wx:if="{{item.content}}">{{item.content}}</text>

            <!-- 图片展示 -->
            <view class="content-images" wx:if="{{item.images && item.images.length > 0}}">
              <image
                class="content-image {{item.images.length === 1 ? 'single' : 'multiple'}}"
                wx:for="{{item.images}}"
                wx:for-item="image"
                wx:key="*this"
                src="{{image}}"
                mode="aspectFill"
                lazy-load="true"></image>
            </view>

            <!-- 标签 -->
            <view class="content-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <text class="content-tag" wx:for="{{item.tags}}" wx:key="*this">#{{item}}</text>
            </view>
          </view>

          <!-- 卡片底部 -->
          <view class="card-footer">
            <view class="action-group">
              <view
                class="action-btn like-btn {{item.isLiked ? 'liked' : ''}}"
                data-id="{{item.id}}"
                catchtap="onLikeTap">
                <text class="action-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
                <text class="action-text">{{item.likes || 0}}</text>
              </view>

              <view
                class="action-btn comment-btn"
                data-id="{{item.id}}"
                catchtap="onCommentTap">
                <text class="action-icon">💬</text>
                <text class="action-text">{{item.comments || 0}}</text>
              </view>

              <view
                class="action-btn share-btn"
                data-id="{{item.id}}"
                catchtap="onShareTap">
                <text class="action-icon">📤</text>
                <text class="action-text">分享</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多状态 -->
      <view class="load-more-state" wx:if="{{posts.length > 0}}">
        <view class="loading-more" wx:if="{{isLoadingMore}}">
          <view class="loading-spinner small"></view>
          <text class="loading-text">加载更多...</text>
        </view>
        <text class="no-more-text" wx:elif="{{!hasMore}}">没有更多内容了</text>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isLoading && posts.length === 0}}">
      <view class="empty-icon">📝</view>
      <text class="empty-title">暂无内容</text>
      <text class="empty-desc">成为第一个分享的人吧</text>
      <view class="empty-action" bindtap="onCreatePost">
        <text class="action-text">立即创建</text>
      </view>
    </view>
  </view>

  <!-- 悬浮创建按钮 -->
  <view class="fab-button" bindtap="onCreatePost" hover-class="fab-hover">
    <text class="fab-icon">✏️</text>
  </view>
</view>