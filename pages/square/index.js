// pages/square/index.js

// 导入统一数据管理器
const localDataManager = require('../../utils/local-data-manager');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

// 定义缓存键和超时时间
const TAG_CACHE_KEY = 'square_tags_cache';
const TAG_CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

const app = getApp(); // 获取 App 实例

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 标签相关
    currentCategory: 'all',
    allCategories: [],
    tagScrollLeft: 0,

    // 内容相关
    posts: [],
    isLoading: true,
    isRefreshing: false,
    isLoadingMore: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,

    // UI状态
    hasNewNotification: false,
    isDarkMode: false,
    isAppTestMode: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '社区广场'
    });

    // 记录 App 的测试模式状态
    this.setData({
      isAppTestMode: app.globalData.isTestMode,
      isDarkMode: wx.getStorageSync('themeMode') === 'dark'
    });

    // 初始化数据
    this.initializeData();
  },

  /**
   * 初始化数据
   */
  initializeData() {
    // 加载标签
    this.loadTags();
    // 加载内容
    this.loadPosts();
  },

  /**
   * 加载标签
   */
  loadTags() {
    try {
      const tags = localDataManager.getSquareTags();
      this.setData({
        allCategories: tags
      });
      this.saveTagsToCache(tags);
    } catch (err) {
      console.error('加载标签失败:', err);
      // 使用默认标签
      const defaultTags = [{ id: 'all', name: '推荐' }];
      this.setData({
        allCategories: defaultTags
      });
    }
  },

  /**
   * 加载帖子数据
   */
  loadPosts(refresh = false) {
    if (refresh) {
      this.setData({
        pageNum: 1,
        hasMore: true,
        isRefreshing: true
      });
    } else {
      this.setData({ isLoading: true });
    }

    try {
      const result = localDataManager.getSquarePosts(
        this.data.currentCategory,
        refresh ? 1 : this.data.pageNum,
        this.data.pageSize
      );

      // 处理帖子数据，添加时间格式化等
      const processedPosts = this.processPosts(result.posts);

      this.setData({
        posts: refresh ? processedPosts : this.data.posts.concat(processedPosts),
        hasMore: result.hasMore,
        isLoading: false,
        isRefreshing: false,
        pageNum: refresh ? 2 : this.data.pageNum + 1
      });

    } catch (err) {
      console.error('加载帖子失败:', err);
      this.setData({
        isLoading: false,
        isRefreshing: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理帖子数据
   */
  processPosts(posts) {
    return posts.map(post => ({
      ...post,
      timeAgo: this.formatTimeAgo(post.createTime),
      author: {
        name: post.author?.name || '匿名用户',
        avatar: post.author?.avatar || '/assets/images/default-avatar.png'
      },
      images: post.images || [],
      tags: post.tags || []
    }));
  },

  /**
   * 格式化时间
   */
  formatTimeAgo(timeStr) {
    const now = new Date();
    const time = new Date(timeStr);
    const diff = now - time;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return time.toLocaleDateString();
  },

  /**
   * 搜索点击事件
   */
  onSearchTap() {
    // 跳转到搜索页面
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },

  /**
   * 通知点击事件
   */
  onNotificationTap() {
    // 跳转到通知页面
    wx.navigateTo({
      url: '/pages/notifications/index'
    });
  },

  /**
   * 标签点击事件
   */
  onTagTap(e) {
    const category = e.currentTarget.dataset.category;
    if (category && category !== this.data.currentCategory) {
      this.setData({
        currentCategory: category
      });

      // 重新加载内容
      this.loadPosts(true);

      // 触感反馈
      wx.vibrateShort({ type: 'light' });
    }
  },

  /**
   * 帖子点击事件
   */
  onPostTap(e) {
    const postId = e.currentTarget.dataset.id;
    if (postId) {
      wx.navigateTo({
        url: `/pages/post-detail/index?id=${postId}`
      });
    }
  },

  /**
   * 点赞事件
   */
  onLikeTap(e) {
    const postId = e.currentTarget.dataset.id;
    const posts = this.data.posts.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          isLiked: !post.isLiked,
          likes: post.isLiked ? post.likes - 1 : post.likes + 1
        };
      }
      return post;
    });

    this.setData({ posts });

    // 触感反馈
    wx.vibrateShort({ type: 'light' });
  },

  /**
   * 评论事件
   */
  onCommentTap(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post-detail/index?id=${postId}&focus=comment`
    });
  },

  /**
   * 分享事件
   */
  onShareTap(e) {
    const postId = e.currentTarget.dataset.id;
    // 这里可以实现分享逻辑
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            break;
          case 1:
            // 分享到朋友圈
            break;
          case 2:
            // 复制链接
            wx.setClipboardData({
              data: `https://example.com/post/${postId}`,
              success: () => {
                wx.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
            break;
        }
      }
    });
  },

  /**
   * 卡片菜单事件
   */
  onCardMenu(e) {
    const postId = e.currentTarget.dataset.id;
    wx.showActionSheet({
      itemList: ['举报', '不感兴趣', '屏蔽作者'],
      success: (res) => {
        // 处理菜单选择
        console.log('菜单选择:', res.tapIndex, postId);
      }
    });
  },

  /**
   * 创建帖子事件
   */
  onCreatePost() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面
    wx.navigateTo({
      url: '/pages/post-create/index'
    });
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.loadPosts(true);
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.isLoadingMore || !this.data.hasMore) {
      return;
    }

    this.setData({ isLoadingMore: true });

    try {
      const result = localDataManager.getSquarePosts(
        this.data.currentCategory,
        this.data.pageNum,
        this.data.pageSize
      );

      const processedPosts = this.processPosts(result.posts);

      this.setData({
        posts: this.data.posts.concat(processedPosts),
        hasMore: result.hasMore,
        isLoadingMore: false,
        pageNum: this.data.pageNum + 1
      });

    } catch (err) {
      console.error('加载更多失败:', err);
      this.setData({ isLoadingMore: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 将标签保存到缓存
   * @param {Array} tags 标签数组
   */
  saveTagsToCache(tags) {
    if (!tags || tags.length === 0) return;

    try {
      wx.setStorageSync(TAG_CACHE_KEY, {
        tags,
        timestamp: Date.now()
      });
      console.log('已将标签保存到缓存');
    } catch (err) {
      console.error('保存标签到缓存出错:', err);
    }
  },



  onReady() {
    // Most ready logic related to scrolling is now in tag-scroll component/behavior
  },

  onShow() {
    // 获取当前主题模式
    const app = getApp();
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 始终更新当前页面的导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      console.log('广场页面 - 更新导航栏样式');
      app.updateCurrentPageNavigationBar();
    }

    // 确保导航栏标题一致
    wx.setNavigationBarTitle({
      title: '社区广场'
    });

    // 使用本地数据，无需检查登录状态变化

    // Logic related to restoring scroll position is now in tag-scroll component/behavior
  },

  /**
   * Event handler for category changes from tag-scroll component
   */
  onCategoryChange(e) {
    const newCategory = e.detail.category;

    if (newCategory && newCategory !== this.data.currentCategory) {
      this.setData({
        currentCategory: newCategory
      });

      // 添加触感反馈
      wx.vibrateShort({ type: 'light' });
    }
  },

  /**
   * 处理标签组件恢复的标签
   * @param {Object} e 事件对象
   */
  onTagsRecovered(e) {
    console.log('收到标签恢复事件:', e.detail);
    const { tags } = e.detail;

    if (tags && Array.isArray(tags) && tags.length > 0) {
      console.log('使用恢复的标签数据:', tags.length);
      this.setData({
        allCategories: tags,
        tagLoading: false
      });

      // 保存到缓存
      this.saveTagsToCache(tags);
    }
  },

  /**
   * 处理标签居中事件
   * 当标签滚动到中心位置时，更新center-indicator和center-bottom-indicator的状态
   * @param {Object} e 事件对象
   */
  onTagCentered(e) {
    console.log('标签居中事件:', e.detail);
    const { categoryId, position } = e.detail;

    // 可以在这里根据需要更新center-indicator和center-bottom-indicator的样式
    // 例如，可以调整它们的位置、大小或动画效果

    // 如果需要，可以获取center-indicator和center-bottom-indicator元素并更新它们
    const query = wx.createSelectorQuery();
    query.select('.center-indicator').boundingClientRect();
    query.select('.center-bottom-indicator').boundingClientRect();
    query.exec(res => {
      if (!res || res.length < 2) return;

      const [centerIndicator, bottomIndicator] = res;

      // 记录日志
      console.log('中心指示器位置:', centerIndicator);
      console.log('底部指示器位置:', bottomIndicator);

      // 这里可以根据标签的位置信息调整指示器的样式
      // 例如，可以通过动态设置样式来增强视觉效果

      // 触发触感反馈，增强用户体验
      wx.vibrateShort({ type: 'light' });
    });
  },

  // 处理主题变化
  onThemeChange: function (newTheme) {
    console.log('广场页面接收到主题变化:', newTheme);

    // 更新页面样式
    const isDarkMode = newTheme === 'dark';

    this.setData({
      isDarkMode: isDarkMode
    });

    console.log('Square theme updated.');
    // 如果需要根据主题刷新瀑布流内容（例如样式不同），可以在这里调用组件的方法
    // const waterfall = this.selectComponent('#waterfall-content');
    // if (waterfall && typeof waterfall.handleThemeChange === 'function') {
    //    waterfall.handleThemeChange(newTheme);
    // }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // Cleanup logic if needed
    this.clearTagLoadingTimeout();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // Cleanup logic if needed, timers are handled by component/behavior now
    this.clearTagLoadingTimeout();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('[Page] onPullDownRefresh triggered');
    // 重新加载本地标签数据
    this.loadTagsFromLocalData();

    // 委托给瀑布流组件处理内容刷新
    const waterfall = this.selectComponent('#waterfall-content'); // Ensure ID is correct in WXML
    if (waterfall && typeof waterfall.handleRefresh === 'function') {
      console.log('[Page] Delegating refresh to waterfall-content component');
      waterfall.handleRefresh();
    } else {
      // Fallback: Directly call the behavior's method if component not found or method missing
      console.warn('[Page] Cannot find waterfall-content component or handleRefresh method. Calling behavior\'s onRefresh directly.');
      // Check if the behavior method exists before calling
      if (typeof this.onRefresh === 'function') {
        this.onRefresh(); // Call behavior's method
      } else {
        console.error('[Page] onRefresh method (from behavior) not found.');
        // Stop the pull-down refresh animation manually if needed
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('[Page] onReachBottom triggered');
    // Prioritize delegating to the component
    const waterfall = this.selectComponent('#waterfall-content'); // Ensure ID is correct in WXML
    if (waterfall && typeof waterfall.handleLoadMore === 'function') {
      console.log('[Page] Delegating load more to waterfall-content component');
      waterfall.handleLoadMore();
    } else {
      // Fallback: Directly call the behavior's method
      console.warn('[Page] Cannot find waterfall-content component or handleLoadMore method. Calling behavior\'s loadMorePosts directly.');
      if (typeof this.loadMorePosts === 'function') {
        this.loadMorePosts(); // Call behavior's method
      } else {
        console.error('[Page] loadMorePosts method (from behavior) not found.');
      }
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '欢迎加入AI互动泡泡社区',
      path: '/pages/square/index'
    };
  }
});