// pages/square/index.js

// 导入统一数据管理器
const localDataManager = require('../../utils/local-data-manager');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

// 引入 Behaviors
const dataLoadingBehavior = require('../../behaviors/data-loading-behavior');
// scroll-behavior is implicitly used by tag-scroll component, Page doesn't need it directly.
// const scrollBehavior = require('../../behaviors/scroll-behavior');

// 定义缓存键和超时时间
const TAG_CACHE_KEY = 'square_tags_cache';
const TAG_CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟
const TAG_LOADING_TIMEOUT = 8000; // 8秒标签加载超时

const app = getApp(); // 获取 App 实例

Page({
  // Use data-loading-behavior to handle posts data and loading states
  // scroll-behavior logic is now encapsulated within tag-scroll component
  behaviors: [dataLoadingBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    // Page specific data
    currentCategory: 'all', // Current selected category, passed to components
    allCategories: [], // Fetched categories, passed to tag-scroll
    // Redundant data removed (posts, leftPosts, rightPosts, isLoading, isLoadingMore, isRefreshing, hasMore, pageNum)
    // Redundant scroll related data removed (scrollIntoView, initialScrollLeft, categoryScrollLeft, centerCategoryId, categoryPositions, windowWidth)
    currentTab: 'square', // Keep tab state if needed for page layout
    tagLoading: false, // 标签加载状态
    isDarkMode: false, // Added for theme handling
    isLoginReady: false, // 新增登录状态标志
    tagLoadingRetryCount: 0, // 标签加载重试次数
    tagLoadingTimer: null, // 用于标签加载超时处理
    isAppTestMode: false // 新增：记录 App 的测试模式状态
  },

  // Page lifecycle methods simplified
  onLoad(options) {
    // Set navigation bar title
    wx.setNavigationBarTitle({
      title: '社区广场'
    });

    // 记录 App 的测试模式状态
    this.setData({ isAppTestMode: app.globalData.isTestMode });

    console.log('开始加载广场标签 - 页面');

    // 直接使用统一数据管理器加载标签
    this.loadTagsFromLocalData();
  },

  /**
   * 从本地数据加载标签
   */
  loadTagsFromLocalData() {
    console.log('从统一数据管理器加载标签');

    try {
      // 直接使用统一数据管理器的标签数据
      const tags = localDataManager.getSquareTags();
      console.log('成功加载本地标签数据:', tags);

      this.setData({
        allCategories: tags,
        tagLoading: false,
        tagLoadingRetryCount: 0
      });

      // 保存到缓存
      this.saveTagsToCache(tags);

    } catch (err) {
      console.error('加载本地标签数据失败:', err);
      // 使用最小默认标签
      const defaultTags = [{ id: 'all', name: '推荐' }];
      this.setData({
        allCategories: defaultTags,
        tagLoading: false
      });
    }
  },

  /**
   * 处理标签加载失败
   * @param {*} error 错误信息
   */
  handleTagLoadingFailure(error) {
    console.error('标签加载失败:', error);

    // 清除超时计时器
    this.clearTagLoadingTimeout();

    // 增加重试计数
    const retryCount = this.data.tagLoadingRetryCount + 1;

    if (retryCount <= 3) {
      // 最多重试3次，使用退避策略
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount - 1), 5000);
      console.log(`将在 ${retryDelay}ms 后进行第 ${retryCount} 次重试`);

      this.setData({ tagLoadingRetryCount: retryCount });

      setTimeout(() => {
        this.loadTagsRobust();
      }, retryDelay);
    } else {
      // 重试次数用尽，显示错误并使用默认标签
      console.error('标签加载重试次数用尽，使用默认标签');

      // 使用最小默认标签
      const defaultTags = [
        { id: 'all', name: '推荐' }
      ];

      this.setData({
        allCategories: defaultTags,
        tagLoading: false,
        tagLoadingRetryCount: 0
      });
    }
  },

  /**
   * 设置标签加载超时处理
   */
  setTagLoadingTimeout() {
    this.data.tagLoadingTimer = setTimeout(() => {
      console.warn('标签加载超时');
      this.handleTagLoadingFailure('加载超时');
    }, TAG_LOADING_TIMEOUT);
  },

  /**
   * 清除标签加载超时计时器
   */
  clearTagLoadingTimeout() {
    if (this.data.tagLoadingTimer) {
      clearTimeout(this.data.tagLoadingTimer);
      this.data.tagLoadingTimer = null;
    }
  },

  /**
   * 从缓存加载标签
   * @returns {Array|null} 标签数组或null
   */
  loadTagsFromCache() {
    try {
      const cacheData = wx.getStorageSync(TAG_CACHE_KEY);
      if (cacheData) {
        const { tags, timestamp } = cacheData;
        const now = Date.now();

        if (now - timestamp < TAG_CACHE_EXPIRY && tags && tags.length > 0) {
          console.log('使用缓存的标签数据');
          return tags;
        } else {
          console.log('缓存标签已过期或无效');
        }
      }
    } catch (err) {
      console.error('读取缓存标签出错:', err);
    }
    return null;
  },

  /**
   * 将标签保存到缓存
   * @param {Array} tags 标签数组
   */
  saveTagsToCache(tags) {
    if (!tags || tags.length === 0) return;

    try {
      wx.setStorageSync(TAG_CACHE_KEY, {
        tags,
        timestamp: Date.now()
      });
      console.log('已将标签保存到缓存');
    } catch (err) {
      console.error('保存标签到缓存出错:', err);
    }
  },



  onReady() {
    // Most ready logic related to scrolling is now in tag-scroll component/behavior
  },

  onShow() {
    // 获取当前主题模式
    const app = getApp();
    const themeMode = wx.getStorageSync('themeMode') || 'light';

    // 始终更新当前页面的导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      console.log('广场页面 - 更新导航栏样式');
      app.updateCurrentPageNavigationBar();
    }

    // 确保导航栏标题一致
    wx.setNavigationBarTitle({
      title: '社区广场'
    });

    // 使用本地数据，无需检查登录状态变化

    // Logic related to restoring scroll position is now in tag-scroll component/behavior
  },

  /**
   * Event handler for category changes from tag-scroll component
   */
  onCategoryChange(e) {
    const newCategory = e.detail.category;

    if (newCategory && newCategory !== this.data.currentCategory) {
      this.setData({
        currentCategory: newCategory
      });

      // 添加触感反馈
      wx.vibrateShort({ type: 'light' });
    }
  },

  /**
   * 处理标签组件恢复的标签
   * @param {Object} e 事件对象
   */
  onTagsRecovered(e) {
    console.log('收到标签恢复事件:', e.detail);
    const { tags } = e.detail;

    if (tags && Array.isArray(tags) && tags.length > 0) {
      console.log('使用恢复的标签数据:', tags.length);
      this.setData({
        allCategories: tags,
        tagLoading: false
      });

      // 保存到缓存
      this.saveTagsToCache(tags);
    }
  },

  /**
   * 处理标签居中事件
   * 当标签滚动到中心位置时，更新center-indicator和center-bottom-indicator的状态
   * @param {Object} e 事件对象
   */
  onTagCentered(e) {
    console.log('标签居中事件:', e.detail);
    const { categoryId, position } = e.detail;

    // 可以在这里根据需要更新center-indicator和center-bottom-indicator的样式
    // 例如，可以调整它们的位置、大小或动画效果

    // 如果需要，可以获取center-indicator和center-bottom-indicator元素并更新它们
    const query = wx.createSelectorQuery();
    query.select('.center-indicator').boundingClientRect();
    query.select('.center-bottom-indicator').boundingClientRect();
    query.exec(res => {
      if (!res || res.length < 2) return;

      const [centerIndicator, bottomIndicator] = res;

      // 记录日志
      console.log('中心指示器位置:', centerIndicator);
      console.log('底部指示器位置:', bottomIndicator);

      // 这里可以根据标签的位置信息调整指示器的样式
      // 例如，可以通过动态设置样式来增强视觉效果

      // 触发触感反馈，增强用户体验
      wx.vibrateShort({ type: 'light' });
    });
  },

  // 处理主题变化
  onThemeChange: function (newTheme) {
    console.log('广场页面接收到主题变化:', newTheme);

    // 更新页面样式
    const isDarkMode = newTheme === 'dark';

    this.setData({
      isDarkMode: isDarkMode
    });

    console.log('Square theme updated.');
    // 如果需要根据主题刷新瀑布流内容（例如样式不同），可以在这里调用组件的方法
    // const waterfall = this.selectComponent('#waterfall-content');
    // if (waterfall && typeof waterfall.handleThemeChange === 'function') {
    //    waterfall.handleThemeChange(newTheme);
    // }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // Cleanup logic if needed
    this.clearTagLoadingTimeout();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // Cleanup logic if needed, timers are handled by component/behavior now
    this.clearTagLoadingTimeout();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('[Page] onPullDownRefresh triggered');
    // 重新加载本地标签数据
    this.loadTagsFromLocalData();

    // 委托给瀑布流组件处理内容刷新
    const waterfall = this.selectComponent('#waterfall-content'); // Ensure ID is correct in WXML
    if (waterfall && typeof waterfall.handleRefresh === 'function') {
      console.log('[Page] Delegating refresh to waterfall-content component');
      waterfall.handleRefresh();
    } else {
      // Fallback: Directly call the behavior's method if component not found or method missing
      console.warn('[Page] Cannot find waterfall-content component or handleRefresh method. Calling behavior\'s onRefresh directly.');
      // Check if the behavior method exists before calling
      if (typeof this.onRefresh === 'function') {
        this.onRefresh(); // Call behavior's method
      } else {
        console.error('[Page] onRefresh method (from behavior) not found.');
        // Stop the pull-down refresh animation manually if needed
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('[Page] onReachBottom triggered');
    // Prioritize delegating to the component
    const waterfall = this.selectComponent('#waterfall-content'); // Ensure ID is correct in WXML
    if (waterfall && typeof waterfall.handleLoadMore === 'function') {
      console.log('[Page] Delegating load more to waterfall-content component');
      waterfall.handleLoadMore();
    } else {
      // Fallback: Directly call the behavior's method
      console.warn('[Page] Cannot find waterfall-content component or handleLoadMore method. Calling behavior\'s loadMorePosts directly.');
      if (typeof this.loadMorePosts === 'function') {
        this.loadMorePosts(); // Call behavior's method
      } else {
        console.error('[Page] loadMorePosts method (from behavior) not found.');
      }
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '欢迎加入AI互动泡泡社区',
      path: '/pages/square/index'
    };
  }
});