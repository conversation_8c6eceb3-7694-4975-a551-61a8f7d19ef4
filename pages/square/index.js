// pages/square/index.js

// 导入统一数据管理器
const localDataManager = require('../../utils/local-data-manager');
// 导入统计适配器
const statisticsAdapter = require('../../utils/statistics-adapter');

// 定义缓存键和超时时间
const TAG_CACHE_KEY = 'square_tags_cache';
const TAG_CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

const app = getApp(); // 获取 App 实例

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 标签相关
    currentCategory: 'all',
    allCategories: [],
    tagScrollLeft: 0,

    // 内容相关
    posts: [],
    isLoading: true,
    isRefreshing: false,
    isLoadingMore: false,
    hasMore: true,
    pageNum: 1,
    pageSize: 10,

    // UI状态
    hasNewNotification: false,
    isDarkMode: false,
    isAppTestMode: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '社区广场'
    });

    // 记录 App 的测试模式状态
    this.setData({
      isAppTestMode: app.globalData.isTestMode,
      isDarkMode: wx.getStorageSync('themeMode') === 'dark'
    });

    // 初始化数据
    this.initializeData();
  },

  /**
   * 初始化数据
   */
  initializeData() {
    // 加载标签
    this.loadTags();
    // 加载内容
    this.loadPosts();
  },

  /**
   * 加载标签
   */
  loadTags() {
    try {
      const tags = localDataManager.getSquareTags();
      this.setData({
        allCategories: tags
      }, () => {
        // 标签加载完成后，让默认选中的标签居中
        this.scrollToActiveTag(this.data.currentCategory);
      });
      this.saveTagsToCache(tags);
    } catch (err) {
      console.error('加载标签失败:', err);
      // 使用默认标签
      const defaultTags = [{ id: 'all', name: '推荐' }];
      this.setData({
        allCategories: defaultTags
      }, () => {
        this.scrollToActiveTag(this.data.currentCategory);
      });
    }
  },

  /**
   * 加载帖子数据
   */
  loadPosts(refresh = false) {
    if (refresh) {
      this.setData({
        pageNum: 1,
        hasMore: true,
        isRefreshing: true
      });
    } else {
      this.setData({ isLoading: true });
    }

    try {
      const result = localDataManager.getSquarePosts(
        this.data.currentCategory,
        refresh ? 1 : this.data.pageNum,
        this.data.pageSize
      );

      // 处理帖子数据，添加时间格式化等
      const processedPosts = this.processPosts(result.posts);

      this.setData({
        posts: refresh ? processedPosts : this.data.posts.concat(processedPosts),
        hasMore: result.hasMore,
        isLoading: false,
        isRefreshing: false,
        pageNum: refresh ? 2 : this.data.pageNum + 1
      });

    } catch (err) {
      console.error('加载帖子失败:', err);
      this.setData({
        isLoading: false,
        isRefreshing: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理帖子数据
   */
  processPosts(posts) {
    return posts.map(post => ({
      ...post,
      timeAgo: this.formatTimeAgo(post.createTime),
      author: {
        name: post.author?.name || '匿名用户',
        avatar: post.author?.avatar || '',
        firstChar: (post.author?.name || '用').charAt(0),
        colorIndex: post.author?.name ? post.author.name.charCodeAt(0) % 7 : 0
      },
      images: post.images || [],
      tags: post.tags || []
    }));
  },

  /**
   * 格式化时间
   */
  formatTimeAgo(timeStr) {
    try {
      const now = new Date();
      let time;

      // 处理不同的日期格式
      if (timeStr.includes('T')) {
        // ISO格式：2024-01-15T10:30:00
        time = new Date(timeStr);
      } else if (timeStr.includes(' ')) {
        // 空格格式：2024-01-15 10:30:00，转换为ISO格式
        time = new Date(timeStr.replace(' ', 'T'));
      } else {
        // 其他格式
        time = new Date(timeStr);
      }

      // 检查日期是否有效
      if (isNaN(time.getTime())) {
        console.warn('无效的日期格式:', timeStr);
        return '未知时间';
      }

      const diff = now - time;

      // 如果是未来时间，显示为刚刚
      if (diff < 0) return '刚刚';

      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (minutes < 1) return '刚刚';
      if (minutes < 60) return `${minutes}分钟前`;
      if (hours < 24) return `${hours}小时前`;
      if (days < 7) return `${days}天前`;

      // 超过7天显示具体日期
      return time.toLocaleDateString('zh-CN', {
        month: 'numeric',
        day: 'numeric'
      });
    } catch (error) {
      console.error('日期格式化错误:', error, timeStr);
      return '未知时间';
    }
  },

  /**
   * 搜索点击事件
   */
  onSearchTap() {
    // 跳转到搜索页面
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },

  /**
   * 通知点击事件
   */
  onNotificationTap() {
    // 跳转到通知页面
    wx.navigateTo({
      url: '/pages/notifications/index'
    });
  },

  /**
   * 标签点击事件
   */
  onTagTap(e) {
    const category = e.currentTarget.dataset.category;
    if (category && category !== this.data.currentCategory) {
      this.setData({
        currentCategory: category
      });

      // 自动滚动到选中的标签
      this.scrollToActiveTag(category);

      // 重新加载内容
      this.loadPosts(true);

      // 触感反馈
      wx.vibrateShort({ type: 'light' });
    }
  },

  /**
   * 滚动到选中的标签
   */
  scrollToActiveTag(category) {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      this.centerActiveTag(category);
    }, 150);
  },

  /**
   * 让选中的标签居中显示
   */
  centerActiveTag(category) {
    const query = wx.createSelectorQuery();
    query.select('.tags-scroll').boundingClientRect();
    query.selectAll('.tag-item').boundingClientRect();
    query.exec((res) => {
      if (!res || !res[0] || !res[1]) return;

      const scrollContainer = res[0];
      const tagElements = res[1];
      const activeIndex = this.data.allCategories.findIndex(item => item.id === category);

      if (activeIndex >= 0 && tagElements[activeIndex] && scrollContainer) {
        const activeTag = tagElements[activeIndex];
        const containerWidth = scrollContainer.width;

        // 计算标签的中心点相对于滚动容器的位置
        let tagLeft = 0;
        for (let i = 0; i < activeIndex; i++) {
          if (tagElements[i]) {
            tagLeft += tagElements[i].width + 8; // 8rpx是gap
          }
        }
        tagLeft += 32; // 左边padding

        // 计算标签中心点
        const tagCenterX = tagLeft + activeTag.width / 2;

        // 计算容器中心点
        const containerCenterX = containerWidth / 2;

        // 计算需要滚动的距离
        const scrollLeft = tagCenterX - containerCenterX;

        // 确保滚动位置在合理范围内
        const finalScrollLeft = Math.max(0, scrollLeft);

        console.log('居中标签:', category, '滚动位置:', finalScrollLeft);

        this.setData({
          tagScrollLeft: finalScrollLeft
        });
      }
    });
  },

  /**
   * 计算所有标签的总宽度
   */
  getTotalTagsWidth() {
    const query = wx.createSelectorQuery();
    query.select('.tags-container').boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        return res[0].width;
      }
      return 0;
    });
  },

  /**
   * 帖子点击事件
   */
  onPostTap(e) {
    const postId = e.currentTarget.dataset.id;
    if (postId) {
      wx.navigateTo({
        url: `/pages/post-detail/index?id=${postId}`
      });
    }
  },

  /**
   * 点赞事件
   */
  onLikeTap(e) {
    const postId = e.currentTarget.dataset.id;
    const posts = this.data.posts.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          isLiked: !post.isLiked,
          likes: post.isLiked ? post.likes - 1 : post.likes + 1
        };
      }
      return post;
    });

    this.setData({ posts });

    // 触感反馈
    wx.vibrateShort({ type: 'light' });
  },

  /**
   * 评论事件
   */
  onCommentTap(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post-detail/index?id=${postId}&focus=comment`
    });
  },

  /**
   * 分享事件
   */
  onShareTap(e) {
    const postId = e.currentTarget.dataset.id;
    // 这里可以实现分享逻辑
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            break;
          case 1:
            // 分享到朋友圈
            break;
          case 2:
            // 复制链接
            wx.setClipboardData({
              data: `https://example.com/post/${postId}`,
              success: () => {
                wx.showToast({
                  title: '链接已复制',
                  icon: 'success'
                });
              }
            });
            break;
        }
      }
    });
  },

  /**
   * 卡片菜单事件
   */
  onCardMenu(e) {
    const postId = e.currentTarget.dataset.id;
    wx.showActionSheet({
      itemList: ['举报', '不感兴趣', '屏蔽作者'],
      success: (res) => {
        // 处理菜单选择
        console.log('菜单选择:', res.tapIndex, postId);
      }
    });
  },

  /**
   * 创建帖子事件
   */
  onCreatePost() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面
    wx.navigateTo({
      url: '/pages/post-create/index'
    });
  },

  /**
   * 头像加载错误处理
   */
  onAvatarError(e) {
    const index = e.currentTarget.dataset.index;
    const posts = this.data.posts;
    if (posts[index]) {
      posts[index].author.avatar = ''; // 清空头像，显示文字头像
      this.setData({ posts });
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.loadPosts(true);
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.isLoadingMore || !this.data.hasMore) {
      return;
    }

    this.setData({ isLoadingMore: true });

    try {
      const result = localDataManager.getSquarePosts(
        this.data.currentCategory,
        this.data.pageNum,
        this.data.pageSize
      );

      const processedPosts = this.processPosts(result.posts);

      this.setData({
        posts: this.data.posts.concat(processedPosts),
        hasMore: result.hasMore,
        isLoadingMore: false,
        pageNum: this.data.pageNum + 1
      });

    } catch (err) {
      console.error('加载更多失败:', err);
      this.setData({ isLoadingMore: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 将标签保存到缓存
   * @param {Array} tags 标签数组
   */
  saveTagsToCache(tags) {
    if (!tags || tags.length === 0) return;

    try {
      wx.setStorageSync(TAG_CACHE_KEY, {
        tags,
        timestamp: Date.now()
      });
      console.log('已将标签保存到缓存');
    } catch (err) {
      console.error('保存标签到缓存出错:', err);
    }
  },



  /**
   * 页面准备完毕
   */
  onReady() {
    // 页面渲染完成
  },

  /**
   * 页面显示
   */
  onShow() {
    // 更新主题模式
    const themeMode = wx.getStorageSync('themeMode') || 'light';
    this.setData({
      isDarkMode: themeMode === 'dark'
    });

    // 更新导航栏样式
    if (app && app.updateCurrentPageNavigationBar) {
      app.updateCurrentPageNavigationBar();
    }

    // 确保选中的标签居中显示
    if (this.data.allCategories.length > 0) {
      this.scrollToActiveTag(this.data.currentCategory);
    }
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 页面隐藏时的清理工作
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 页面卸载时的清理工作
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadPosts(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉触底
   */
  onReachBottom() {
    this.onLoadMore();
  },

  /**
   * 保存标签到缓存
   */
  saveTagsToCache(tags) {
    try {
      const cacheData = {
        tags: tags,
        timestamp: Date.now()
      };
      wx.setStorageSync(TAG_CACHE_KEY, cacheData);
      console.log('已将标签保存到缓存');
    } catch (err) {
      console.error('保存标签到缓存出错:', err);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '欢迎加入AI互动泡泡社区',
      path: '/pages/square/index'
    };
  }
});