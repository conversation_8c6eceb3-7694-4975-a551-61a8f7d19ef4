// behaviors/data-loading-behavior.js
// 数据加载行为，用于广场页面和瀑布流组件

// 导入统一数据管理器
const localDataManager = require('../utils/local-data-manager');
const app = getApp(); // 获取 App 实例

module.exports = Behavior({
  properties: {
    // 页面大小
    pageSize: {
      type: Number,
      value: 8
    }
  },

  data: {
    posts: [],
    leftPosts: [],
    rightPosts: [],
    isLoading: true,
    isLoadingMore: false,
    isRefreshing: false,
    hasMore: true,
    pageNum: 1,
    isLoginReady: false, // 使用 app 的状态
    showLoginPrompt: false // 控制登录提示 (如果需要)
  },

  attached() {
    // 检查用户认证状态，并保存到数据中
    this.checkAuthStatus();
  },

  methods: {
    /**
     * 检查用户认证状态
     */
    checkAuthStatus() {
      // 避免频繁检查
      const now = Date.now();
      if (now - this.data.lastAuthCheck < 30000) { // 30秒内不重复检查
        return this.data.isAuthenticated;
      }

      const token = wx.getStorageSync('token');
      const isAuthenticated = !!token;

      // 更新状态
      this.setData({
        isAuthenticated,
        lastAuthCheck: now
      });

      return isAuthenticated;
    },

    /**
     * 加载初始帖子
     * @param {string} categoryId - 当前分类 ID (由 Page 传入)
     * @param {boolean} forceRefresh - 是否强制刷新 (由 Page 传入)
     */
    loadInitialPosts(categoryId = 'all', forceRefresh = false) {
      // 如果不是强制刷新且正在加载，则阻止
      if (!forceRefresh && this.data.isLoading) {
        console.log('Behavior: 正在加载中，忽略 loadInitialPosts');
        return;
      }

      this.setData({
        isLoading: true,
        pageNum: 1,
        hasMore: true,
        posts: [], // 清空旧数据
        leftPosts: [],
        rightPosts: [],
        showLoginPrompt: false // 重置登录提示
      });

      console.log('Behavior: 从本地数据管理器加载帖子，分类:', categoryId);

      try {
        // 直接使用统一数据管理器的帖子数据
        const result = localDataManager.getSquarePosts(categoryId, 1, this.data.pageSize);
        const posts = result.posts;

        console.log('Behavior: 成功加载本地帖子数据:', posts.length);

        const { leftPosts, rightPosts } = this.splitPostsIntoColumns(posts);
        this.setData({
          posts,
          leftPosts,
          rightPosts,
          isLoading: false,
          hasMore: result.hasMore
        });

      } catch (err) {
        console.error('Behavior: 加载本地帖子数据失败', err);
        this.setData({
          isLoading: false,
          posts: [],
          leftPosts: [],
          rightPosts: [],
          hasMore: false
        });
        wx.showToast({ title: '加载失败', icon: 'none' });
      }
    },



    /**
     * 获取默认标签（已废弃，返回空数组）
     * @deprecated 不再使用模拟数据，只使用真实数据
     */
    getDefaultTags() {
      console.warn('getDefaultTags 方法已废弃，不再使用模拟数据');
      return [];
    },

    /**
     * 记录查看广场的学习活动
     */
    recordViewActivity() {
      // 检查用户是否登录
      const isAuthenticated = this.checkAuthStatus();
      if (!isAuthenticated) {
        console.log('用户未登录，不记录学习活动');
        return;
      }

      statisticsAPI.recordActivity({
        activityType: 'view_insight',
        contentType: 'note',
        details: {
          view: 'square',
          category: this.data.currentCategory,
          count: this.data.posts.length
        }
      }).catch(err => {
        console.error('记录学习活动失败', err);
      });
    },

    /**
     * 加载模拟数据（已废弃）
     * @deprecated 不再使用模拟数据，只使用真实数据
     */
    loadMockData() {
      console.warn('loadMockData 方法已废弃，不再使用模拟数据');
      this.setData({
        posts: [],
        leftPosts: [],
        rightPosts: [],
        isLoading: false,
        hasMore: false
      });
    },

    /**
     * 加载更多帖子
     */
    loadMorePosts() {
      if (this.data.isLoadingMore || !this.data.hasMore) {
        return;
      }
      this.setData({ isLoadingMore: true });
      const nextPage = this.data.pageNum + 1;

      console.log('Behavior: 从本地数据管理器加载更多帖子，页码:', nextPage);

      try {
        // 使用统一数据管理器获取更多帖子
        const result = localDataManager.getSquarePosts(
          this.data.currentCategory || 'all',
          nextPage,
          this.data.pageSize
        );
        const newPosts = result.posts;

        if (newPosts.length > 0) {
          const combinedPosts = this.data.posts.concat(newPosts);
          const { leftPosts, rightPosts } = this.splitPostsIntoColumns(combinedPosts);

          this.setData({
            posts: combinedPosts,
            leftPosts,
            rightPosts,
            pageNum: nextPage,
            isLoadingMore: false,
            hasMore: result.hasMore
          });
        } else {
          this.setData({ isLoadingMore: false, hasMore: false });
        }
      } catch (err) {
        console.error('Behavior: 加载更多本地帖子失败', err);
        this.setData({ isLoadingMore: false, hasMore: false });
      }
    },

    /**
     * 刷新数据
     */
    onRefresh() {
      if (this.data.isLoading) return;

      this.setData({
        isRefreshing: true
      });

      setTimeout(() => {
        this.loadInitialPosts();
        this.setData({
          isRefreshing: false
        });
      }, 1000);
    },

    /**
     * 根据分类获取帖子（已废弃）
     * @deprecated 不再使用模拟数据，只使用真实数据
     * @param {string} category - 分类名称
     * @param {number} page - 页码
     * @returns {Array} 帖子数组
     */
    getPostsByCategory(category, page) {
      console.warn('getPostsByCategory 方法已废弃，不再使用模拟数据');
      return [];
    },

    /**
     * 将帖子分为左右两列
     * @param {Array} posts - 所有帖子
     * @returns {Object} 包含左列和右列的对象
     */
    splitPostsIntoColumns(posts) {
      const leftPosts = [];
      const rightPosts = [];

      posts.forEach((post, index) => {
        if (index % 2 === 0) {
          leftPosts.push(post);
        } else {
          rightPosts.push(post);
        }
      });

      return { leftPosts, rightPosts };
    },

    /**
     * 点赞帖子
     */
    likePost(e) {
      const { postId } = e.currentTarget.dataset;
      const currentPosts = this.data.posts;
      const postIndex = currentPosts.findIndex(p => p.id === postId);

      if (postIndex === -1) return;

      const post = currentPosts[postIndex];
      const isLiked = post.isLiked;
      const newLikes = isLiked ? post.likes - 1 : post.likes + 1;

      // 直接更新UI，使用本地数据
      this.setData({
        [`posts[${postIndex}].isLiked`]: !isLiked,
        [`posts[${postIndex}].likes`]: newLikes
      });
      // Update columns as well for consistency
      this.updateColumnPosts(postId, { isLiked: !isLiked, likes: newLikes });

      // 添加触感反馈
      wx.vibrateShort({ type: 'light' });

      console.log('点赞/取消点赞成功 (本地数据)');
    },

    // Helper to update posts in left/right columns
    updateColumnPosts(postId, updates) {
      const leftIndex = this.data.leftPosts.findIndex(p => p.id === postId);
      if (leftIndex !== -1) {
        for (const key in updates) {
          this.setData({ [`leftPosts[${leftIndex}].${key}`]: updates[key] });
        }
      } else {
        const rightIndex = this.data.rightPosts.findIndex(p => p.id === postId);
        if (rightIndex !== -1) {
          for (const key in updates) {
            this.setData({ [`rightPosts[${rightIndex}].${key}`]: updates[key] });
          }
        }
      }
    },

    /**
     * 查看帖子详情
     */
    viewPostDetail(e) {
      const postId = e.currentTarget.dataset.id;

      wx.showToast({
        title: '查看详情: ' + postId,
        icon: 'none'
      });

      // 实际项目中，这里会跳转到详情页
      // wx.navigateTo({
      //   url: `/pages/post-detail/index?id=${postId}`
      // });
    }
  }
});
