/**
 * 统一的本地数据管理器
 * 所有页面的数据都从这里获取，确保数据一致性
 */

// 颜色配置
const COLORS = {
  blue: '#3B82F6',
  lime: '#84CC16',
  sky: '#0EA5E9',
  amber: '#F59E0B',
  purple: '#8B5CF6',
  green: '#22c55e',
  indigo: '#6366F1',
  teal: '#14B8A6',
  rose: '#E11D48'
};

// 图标配置
const ICONS = {
  platform: '📱',
  bubble: '💫',
  explore: '🔍',
  plan: '📝',
  notes: '💡',
  communication: '👥',
  learning: '📚'
};

/**
 * 泡泡主题数据（首页使用）
 */
const BUBBLE_THEMES = [
  {
    id: '101',
    name: '平台介绍',
    englishName: 'Platform',
    description: '了解AIBUBB平台的核心功能和使用技巧',
    color: COLORS.blue,
    icon: ICONS.platform
  },
  {
    id: '102',
    name: '泡泡功能',
    englishName: 'Bubbles',
    description: '探索交互式泡泡的使用方法',
    color: COLORS.lime,
    icon: ICONS.bubble
  },
  {
    id: '103',
    name: '广场探索',
    englishName: 'Explore',
    description: '发现社区中的精彩内容和讨论',
    color: COLORS.sky,
    icon: ICONS.explore
  },
  {
    id: '104',
    name: '学习计划',
    englishName: 'Plans',
    description: '制定和管理个性化学习计划',
    color: COLORS.amber,
    icon: ICONS.plan
  },
  {
    id: '105',
    name: '笔记技巧',
    englishName: 'Notes',
    description: '掌握高效记录和组织学习笔记的方法',
    color: COLORS.purple,
    icon: ICONS.notes
  }
];

/**
 * 学习计划数据（学习页面使用）
 */
const LEARNING_PLANS = [
  {
    id: 'plan_001',
    title: 'AIBUBB平台入门指南',
    description: '快速了解AIBUBB平台的核心功能，开启你的学习之旅',
    progress: 0,
    targetDays: 7,
    completedDays: 0,
    status: 'not_started',
    isSystemDefault: true,
    tags: ['平台介绍', '泡泡功能', '广场探索'],
    modules: [
      {
        id: 'module_001',
        title: '平台介绍',
        description: '了解AIBUBB平台的核心理念和功能',
        icon: ICONS.platform,
        color: COLORS.blue,
        progress: 0
      },
      {
        id: 'module_002',
        title: '泡泡功能',
        description: '探索首页泡泡的互动方式和学习体验',
        icon: ICONS.bubble,
        color: COLORS.lime,
        progress: 0
      },
      {
        id: 'module_003',
        title: '广场探索',
        description: '发现和分享学习内容，与他人互动',
        icon: ICONS.explore,
        color: COLORS.sky,
        progress: 0
      }
    ]
  }
];

/**
 * 学习模板数据
 */
const LEARNING_TEMPLATES = [
  {
    id: 'template_001',
    title: '每日学习计划',
    type: '学习计划',
    description: '适合日常学习使用的计划模板，包含学习目标、时间安排和复习提醒',
    color: COLORS.indigo
  },
  {
    id: 'template_002',
    title: '技能提升计划',
    type: '学习计划',
    description: '针对特定技能提升的专用模板，包含练习安排和进度跟踪',
    color: COLORS.green
  },
  {
    id: 'template_003',
    title: '学习笔记模板',
    type: '学习记录',
    description: '用于记录学习过程的模板，包含要点总结和反思记录',
    color: COLORS.purple
  }
];

/**
 * 广场标签数据
 */
const SQUARE_TAGS = [
  {
    id: 'platform',
    name: '平台介绍'
  },
  {
    id: 'all',
    name: '推荐'
  },
  {
    id: 'bubbles',
    name: '泡泡功能'
  },
  {
    id: 'explore',
    name: '广场探索'
  },
  {
    id: 'plans',
    name: '学习计划'
  },
  {
    id: 'notes',
    name: '笔记技巧'
  }
];

/**
 * 广场帖子数据
 */
const SQUARE_POSTS = [
  {
    id: 'post_001',
    title: '欢迎来到AIBUBB平台！',
    content: '这里是一个专注于学习和交流的社区平台。你可以在这里分享学习心得、探索新知识、与他人互动交流。',
    author: {
      id: 'user_001',
      name: 'AIBUBB团队',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['platform'],
    images: [],
    likes: 42,
    comments: 8,
    shares: 3,
    createTime: '2024-01-15T10:30:00',
    isLiked: false,
    category: 'platform'
  },
  {
    id: 'post_002',
    title: '如何高效使用泡泡功能',
    content: '首页的泡泡不仅仅是装饰，它们是你学习旅程的入口。点击不同的泡泡可以进入不同的学习主题，每个泡泡都代表一个学习领域。',
    author: {
      id: 'user_002',
      name: '学习达人',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['bubbles'],
    images: [],
    likes: 28,
    comments: 12,
    shares: 5,
    createTime: '2024-01-14T15:20:00',
    isLiked: false,
    category: 'bubbles'
  },
  {
    id: 'post_003',
    title: '我的学习计划制定心得',
    content: '制定学习计划时，我发现最重要的是要设定明确的目标和合理的时间安排。建议大家从小目标开始，逐步建立学习习惯。',
    author: {
      id: 'user_003',
      name: '计划小能手',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['plans'],
    images: [],
    likes: 35,
    comments: 15,
    shares: 7,
    createTime: '2024-01-13T09:45:00',
    isLiked: true,
    category: 'plans'
  },
  {
    id: 'post_004',
    title: '笔记技巧分享：如何做出有效的学习笔记',
    content: '好的笔记不在于多，而在于精。我推荐使用思维导图和关键词标记的方法，这样可以快速回顾和复习。',
    author: {
      id: 'user_004',
      name: '笔记专家',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['notes'],
    images: [],
    likes: 51,
    comments: 20,
    shares: 12,
    createTime: '2024-01-12T14:15:00',
    isLiked: false,
    category: 'notes'
  },
  {
    id: 'post_005',
    title: '广场探索指南：发现更多精彩内容',
    content: '广场是我们交流学习的地方。你可以通过标签筛选感兴趣的内容，也可以发布自己的学习心得与大家分享。',
    author: {
      id: 'user_005',
      name: '探索者',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['explore'],
    images: [],
    likes: 23,
    comments: 6,
    shares: 4,
    createTime: '2024-01-11T16:30:00',
    isLiked: false,
    category: 'explore'
  },
  {
    id: 'post_006',
    title: '平台功能更新：新增学习进度跟踪',
    content: '我们新增了学习进度跟踪功能，现在你可以更直观地看到自己的学习成果和进步轨迹。',
    author: {
      id: 'user_001',
      name: 'AIBUBB团队',
      avatar: 'https://via.placeholder.com/64x64/cccccc/ffffff?text=头像'
    },
    tags: ['platform'],
    images: [],
    likes: 67,
    comments: 25,
    shares: 18,
    createTime: '2024-01-10T11:00:00',
    isLiked: true,
    category: 'platform'
  }
];

/**
 * 数据管理器类
 */
class LocalDataManager {
  constructor() {
    this.initialized = false;
    this.init();
  }

  /**
   * 初始化数据管理器
   */
  init() {
    console.log('LocalDataManager: 初始化本地数据管理器');
    this.initialized = true;
  }

  /**
   * 获取泡泡主题数据
   */
  getBubbleThemes() {
    return [...BUBBLE_THEMES]; // 返回副本，避免外部修改
  }

  /**
   * 获取学习计划数据
   */
  getLearningPlans() {
    return [...LEARNING_PLANS];
  }

  /**
   * 获取学习模板数据
   */
  getLearningTemplates() {
    return [...LEARNING_TEMPLATES];
  }

  /**
   * 根据ID获取特定的学习计划
   */
  getLearningPlanById(planId) {
    return LEARNING_PLANS.find(plan => plan.id === planId) || null;
  }

  /**
   * 获取系统默认学习计划
   */
  getSystemDefaultPlan() {
    return LEARNING_PLANS.find(plan => plan.isSystemDefault) || LEARNING_PLANS[0];
  }

  /**
   * 根据主题名称获取泡泡主题
   */
  getBubbleThemeByName(name) {
    return BUBBLE_THEMES.find(theme => theme.name === name) || null;
  }

  /**
   * 获取颜色配置
   */
  getColors() {
    return { ...COLORS };
  }

  /**
   * 获取图标配置
   */
  getIcons() {
    return { ...ICONS };
  }

  /**
   * 获取广场标签数据
   */
  getSquareTags() {
    return [...SQUARE_TAGS];
  }

  /**
   * 获取广场帖子数据
   */
  getSquarePosts(category = 'all', page = 1, pageSize = 10) {
    let filteredPosts = [...SQUARE_POSTS];

    // 根据分类筛选
    if (category && category !== 'all') {
      filteredPosts = SQUARE_POSTS.filter(post => post.category === category);
    }

    // 按时间倒序排列
    filteredPosts.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

    return {
      posts: paginatedPosts,
      hasMore: endIndex < filteredPosts.length,
      total: filteredPosts.length
    };
  }

  /**
   * 根据ID获取特定帖子
   */
  getSquarePostById(postId) {
    return SQUARE_POSTS.find(post => post.id === postId) || null;
  }

  /**
   * 搜索广场帖子
   */
  searchSquarePosts(keyword) {
    if (!keyword) return [...SQUARE_POSTS];

    const lowerKeyword = keyword.toLowerCase();
    return SQUARE_POSTS.filter(post =>
      post.title.toLowerCase().includes(lowerKeyword) ||
      post.content.toLowerCase().includes(lowerKeyword) ||
      post.author.name.toLowerCase().includes(lowerKeyword)
    );
  }
}

// 创建单例实例
const localDataManager = new LocalDataManager();

module.exports = localDataManager;
